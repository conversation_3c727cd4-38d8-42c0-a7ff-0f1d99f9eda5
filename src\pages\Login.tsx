import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Space } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { login } from '../services/auth';
import { useAuthStore } from '../utils/authStore';
import './Login.css';

const { Title, Text } = Typography;

interface LoginFormValues {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { login: setAuth } = useAuthStore();

  const handleSubmit = async (values: LoginFormValues) => {
    setLoading(true);
    try {
      const user = await login(values.username, values.password);
      setAuth(user);
      message.success('登录成功！');
      navigate('/dashboard');
    } catch (error: any) {
      console.error('Login error:', error);

      // 处理不同类型的错误
      let errorMessage = '登录失败，请稍后重试';

      if (error.response) {
        // 服务器响应错误
        const status = error.response.status;
        const data = error.response.data;

        console.log('Error response:', { status, data });

        if (status === 400) {
          errorMessage = '用户名或密码错误';
        } else if (status === 401) {
          errorMessage = '用户名或密码错误';
        } else if (status === 422) {
          errorMessage = '请求参数格式错误';
        } else if (status === 500) {
          errorMessage = '服务器内部错误，请稍后重试';
        } else if (data && data.detail) {
          errorMessage = typeof data.detail === 'string' ? data.detail : '登录失败';
        } else if (data && data.message) {
          errorMessage = typeof data.message === 'string' ? data.message : '登录失败';
        }
      } else if (error.request) {
        // 网络错误
        errorMessage = '网络连接失败，请检查网络设置';
      } else if (error.message) {
        // 其他错误
        errorMessage = error.message;
      }

      // 确保错误消息显示
      console.log('Showing error message:', errorMessage);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      {/* 添加粒子效果 */}
      <div className="particles">
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
      </div>

      <div className="login-background">
        <div className="login-content">
          <Card className="login-card" variant="borderless">
            <div className="login-header">
              <div className="login-logo-container">
                <img src="/logo.png" alt="锤磨AI" className="login-logo" />
              </div>
              <Title level={2} className="login-title">
                锤磨AI
              </Title>
              <Text type="secondary" className="login-subtitle">
                欢迎登录AI课后陪练系统
              </Text>
            </div>

            <Form
              form={form}
              name="login"
              onFinish={handleSubmit}
              autoComplete="off"
              size="large"
              className="login-form"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 2, message: '用户名至少2个字符' },
                  { max: 50, message: '用户名不能超过50个字符' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="请输入密码"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  icon={<LoginOutlined />}
                  className="login-button"
                >
                  {loading ? '登录中...' : '登录'}
                </Button>
              </Form.Item>
            </Form>

            <div className="login-footer">
              <Space direction="vertical" size="small" style={{ width: '100%', textAlign: 'center' }}>
                <Text type="" style={{ fontSize: '12px' }}>
                  © 2025 Trainingmore.com All rights reserved.
                </Text>
              </Space>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
